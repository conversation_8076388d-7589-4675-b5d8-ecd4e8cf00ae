import { getHeaders } from '@helpers/utils';
import { UserIncident } from '@components/CustomerIncidents/ManageCustomerIncidentsContent';
import useSWR from 'swr';

type CustomerIncidentsResponse = {
  incidents: UserIncident[];
  total: number;
};

type CustomerIncidentsError = {
  message: string;
  status?: number;
};

const fetchWithToken = async (url: string, token: string): Promise<CustomerIncidentsResponse> => {
  if (!token) {
    throw new Error('No authentication token provided');
  }

  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_CUSTOMER_VERIFICATION_SERVICE_API_URL}/api/v1/customer-incidents`,
      {
        method: 'GET',
        headers: getHeaders(token),
      },
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch customer incidents');
    }

    const data = await response.json();
    return {
      incidents: data.incidents || [],
      total: data.total || 0,
    };
  } catch (error) {
    const errorMessage =
      (error as Error).message || 'Error when attempting to fetch customer incidents.';
    throw new Error(errorMessage);
  }
};

export function useCustomerIncidents(token: string) {
  return useSWR<CustomerIncidentsResponse, CustomerIncidentsError>(
    token ? ['/api/v1/customer-incidents', token] : null,
    ([url, token]: [string, string]) => fetchWithToken(url, token),
    {
      revalidateOnFocus: true,
      focusThrottleInterval: 30000, // 30 seconds
    },
  );
}
