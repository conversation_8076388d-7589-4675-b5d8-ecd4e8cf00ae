# Customer Incidents Management

This module provides a complete CRUD interface for managing customer incident reports in the Haven Identity Admin application.

## Features

- **Create**: Add new customer incident reports with validation
- **Read**: View all incidents in a responsive table format
- **Update**: Edit existing incident reports
- **Delete**: Remove incident reports with confirmation

## Components

### ManageCustomerIncidentsContent

Main container component that orchestrates the CRUD operations.

**Features:**

- Page header with title and create button
- Modal state management
- Refresh trigger for data synchronization

### CustomerIncidentsList

Displays incidents in a table format with action buttons.

**Features:**

- Responsive table design
- Loading and error states
- Edit and delete actions
- SWR-based data fetching with automatic refresh

### CustomerIncidentModal

Modal form for creating and editing incidents.

**Features:**

- Form validation using react-hook-form
- Conditional fields (crime reference number when police involved)
- Success/error messaging
- Responsive design

## Data Structure

The `UserIncident` type matches the Joi schema requirements:

```typescript
type UserIncident = {
  id?: string;
  bookingReferenceNumber: string; // Required, 1-100 chars
  leadGuestName: string; // Required, 1-200 chars
  reasonForBan: string; // Required, 1-1000 chars
  isDiscoveredAfterDeparture: boolean; // Required
  isPoliceInvolved?: boolean; // Optional
  crimeReferenceNumber?: string; // Optional, max 100 chars
  otherInformation?: string; // Optional, max 2000 chars
  createdAt?: string;
  updatedAt?: string;
};
```

## API Endpoints Expected

The components expect the following API endpoints to be implemented:

- `GET /identity/api/customer-incidents` - List all incidents
- `POST /identity/api/customer-incidents` - Create new incident
- `PUT /identity/api/customer-incidents/:id` - Update incident
- `DELETE /identity/api/customer-incidents/:id` - Delete incident

## Styling

Uses SCSS modules with responsive design:

- Mobile-first approach
- Accessible color scheme using Haven design tokens
- Hover states and loading indicators
- Modal overlay with proper z-index management

## Testing

Includes unit tests for the main component with mocked dependencies.

## Usage

```tsx
import { ManageCustomerIncidentsContent } from '@components/CustomerIncidents';

// In your page component
export default function ManageCustomerIncidentsPage() {
  return <ManageCustomerIncidentsContent />;
}
```

## Dependencies

- React Hook Form for form management
- SWR for data fetching
- Haven Engineering shared components
- SCSS modules for styling
