'use client';

import { useState, useEffect } from 'react';
import { useF<PERSON>, SubmitHandler, SubmitErrorHandler } from 'react-hook-form';
import { ControlledTextInput } from '@components/ControlledComponents/ControlledTextInput.';
import { ControlledCheckboxInput } from '@components/ControlledComponents/ControlledCheckboxInput';
import { ControlledTextAreaInput } from '@components/ControlledComponents/ControlledTextAreaInput';
import Button, {
  Size,
  Theme,
} from '@havenengineering/module-shared-library/dist/components/Button/Button';
import InlineMessaging, {
  InlineMessagingTheme,
} from '@havenengineering/module-shared-library/dist/components/InlineMessaging/InlineMessaging';
import { UserIncident } from './ManageCustomerIncidentsContent';
import { getHeaders } from '@helpers/utils';
import styles from '@styles/components/CustomerIncidents.module.scss';

type CustomerIncidentModalProps = {
  incident: UserIncident | null;
  onClose: () => void;
  onSaveSuccess: () => void;
  token: string;
};

type FormData = {
  bookingReferenceNumber: string;
  leadGuestName: string;
  reasonForBan: string;
  isDiscoveredAfterDeparture: boolean;
  isPoliceInvolved: boolean;
  crimeReferenceNumber: string;
  otherInformation: string;
};

export const CustomerIncidentModal = ({
  incident,
  onClose,
  onSaveSuccess,
  token,
}: CustomerIncidentModalProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<string>('');

  const isEditing = !!incident;

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      bookingReferenceNumber: '',
      leadGuestName: '',
      reasonForBan: '',
      isDiscoveredAfterDeparture: false,
      isPoliceInvolved: false,
      crimeReferenceNumber: '',
      otherInformation: '',
    },
    mode: 'onBlur',
  });

  const isPoliceInvolved = watch('isPoliceInvolved');

  useEffect(() => {
    if (incident) {
      reset({
        bookingReferenceNumber: incident.bookingReferenceNumber || '',
        leadGuestName: incident.leadGuestName || '',
        reasonForBan: incident.reasonForBan || '',
        isDiscoveredAfterDeparture: incident.isDiscoveredAfterDeparture || false,
        isPoliceInvolved: incident.isPoliceInvolved || false,
        crimeReferenceNumber: incident.crimeReferenceNumber || '',
        otherInformation: incident.otherInformation || '',
      });
    }
  }, [incident, reset]);

  const onSubmit: SubmitHandler<FormData> = async (data) => {
    try {
      setIsLoading(true);
      setError('');
      setSuccess('');

      const url = isEditing
        ? `${process.env.NEXT_PUBLIC_CUSTOMER_VERIFICATION_SERVICE_API_URL}/api/v1/customer-incidents/${incident.id}`
        : `${process.env.NEXT_PUBLIC_CUSTOMER_VERIFICATION_SERVICE_API_URL}/api/v1/customer-incidents`;

      const method = isEditing ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          ...getHeaders(token),
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || `Failed to ${isEditing ? 'update' : 'create'} incident`,
        );
      }

      setSuccess(`Incident ${isEditing ? 'updated' : 'created'} successfully!`);

      // Wait a moment to show success message, then close
      setTimeout(() => {
        onSaveSuccess();
      }, 1500);
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  const onError: SubmitErrorHandler<FormData> = () => {
    setError('Please fix the validation errors before submitting.');
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className={styles.modalOverlay} onClick={handleOverlayClick}>
      <div className={styles.modal}>
        <div className={styles.modalHeader}>
          <h2>{isEditing ? 'Edit Customer Incident' : 'Create New Customer Incident'}</h2>
          <button
            className={styles.closeButton}
            onClick={onClose}
            type="button"
            aria-label="Close modal"
          >
            ×
          </button>
        </div>

        <div className={styles.modalBody}>
          <form
            onSubmit={handleSubmit(onSubmit, onError)}
            className={styles.form}
            data-testid="incident-form"
          >
            <div className={styles.formRow}>
              <ControlledTextInput
                id="bookingReferenceNumber"
                label="Booking Reference Number *"
                control={control}
                rules={{
                  required: 'Booking reference number is required',
                  minLength: {
                    value: 1,
                    message: 'Booking reference number must be at least 1 character',
                  },
                  maxLength: {
                    value: 100,
                    message: 'Booking reference number must be at most 100 characters',
                  },
                }}
                errors={errors}
              />
            </div>

            <div className={styles.formRow}>
              <ControlledTextInput
                id="leadGuestName"
                label="Lead Guest Name *"
                control={control}
                rules={{
                  required: 'Lead guest name is required',
                  minLength: { value: 1, message: 'Lead guest name must be at least 1 character' },
                  maxLength: {
                    value: 200,
                    message: 'Lead guest name must be at most 200 characters',
                  },
                }}
                errors={errors}
              />
            </div>

            <div className={styles.formRow}>
              <ControlledTextAreaInput
                id="reasonForBan"
                label="Reason for Ban *"
                control={control}
                rules={{
                  required: 'Reason for ban is required',
                  minLength: { value: 1, message: 'Reason for ban must be at least 1 character' },
                  maxLength: {
                    value: 1000,
                    message: 'Reason for ban must be at most 1000 characters',
                  },
                }}
                errors={errors}
                rows={4}
              />
            </div>

            <div className={styles.checkboxRow}>
              <ControlledCheckboxInput
                id="isDiscoveredAfterDeparture"
                label="Discovered after departure"
                control={control}
                errors={errors}
              />
            </div>

            <div className={styles.checkboxRow}>
              <ControlledCheckboxInput
                id="isPoliceInvolved"
                label="Police involved"
                control={control}
                errors={errors}
              />
            </div>

            {isPoliceInvolved && (
              <div className={styles.formRow}>
                <ControlledTextInput
                  id="crimeReferenceNumber"
                  label="Crime Reference Number"
                  control={control}
                  rules={{
                    maxLength: {
                      value: 100,
                      message: 'Crime reference number must be at most 100 characters',
                    },
                  }}
                  errors={errors}
                />
              </div>
            )}

            <div className={styles.formRow}>
              <ControlledTextAreaInput
                id="otherInformation"
                label="Other Information"
                control={control}
                rules={{
                  maxLength: {
                    value: 2000,
                    message: 'Other information must be at most 2000 characters',
                  },
                }}
                errors={errors}
                rows={3}
              />
            </div>

            {error && (
              <InlineMessaging
                type={InlineMessagingTheme.ERROR_THEME}
                message={error}
                dismissible={false}
                className={styles.mb15}
              />
            )}

            {success && (
              <InlineMessaging
                type={InlineMessagingTheme.SUCCESS_THEME}
                message={success}
                dismissible={false}
                className={styles.mb15}
              />
            )}

            <div className={styles.modalActions}>
              <Button
                type="button"
                theme={Theme.SECONDARY_THEME}
                scale={Size.MEDIUM_SIZE}
                onClick={onClose}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                theme={Theme.PRIMARY_THEME}
                scale={Size.MEDIUM_SIZE}
                loading={isLoading}
                disabled={isLoading}
                data-testid="save-incident-btn"
              >
                {isEditing ? 'Update Incident' : 'Create Incident'}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};
