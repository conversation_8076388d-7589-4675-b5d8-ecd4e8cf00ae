'use client';

import { useContext, useState } from 'react';
import { PageHeading } from '@components/shared/PageHeading';
import { UserInfoContext } from '@providers/UserInfoProvider';
import { CustomerIncidentsList } from './CustomerIncidentsList';
import { CustomerIncidentModal } from './CustomerIncidentModal';
import Button, {
  Size,
  Theme,
} from '@havenengineering/module-shared-library/dist/components/Button/Button';
import styles from '@styles/components/CustomerIncidents.module.scss';

/**
 * User incident data structure matching the Joi schema requirements
 */
export type UserIncident = {
  id?: string;
  bookingReferenceNumber: string;
  leadGuestName: string;
  reasonForBan: string;
  isDiscoveredAfterDeparture: boolean;
  isPoliceInvolved?: boolean;
  crimeReferenceNumber?: string;
  otherInformation?: string;
  createdAt?: string;
  updatedAt?: string;
};

/**
 * Main component for managing customer incidents with CRUD operations
 * Provides a table view of incidents and modal forms for create/edit operations
 */
export const ManageCustomerIncidentsContent = () => {
  const { token } = useContext(UserInfoContext);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingIncident, setEditingIncident] = useState<UserIncident | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleCreateNew = () => {
    setEditingIncident(null);
    setIsModalOpen(true);
  };

  const handleEdit = (incident: UserIncident) => {
    setEditingIncident(incident);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingIncident(null);
  };

  const handleSaveSuccess = () => {
    setRefreshTrigger((prev) => prev + 1);
    handleModalClose();
  };

  return (
    <div className={styles.container}>
      <PageHeading title="Manage Customer Incidents" />

      <div className={styles.header}>
        <p>Manage customer incident reports and bans</p>
        <Button
          theme={Theme.PRIMARY_THEME}
          scale={Size.MEDIUM_SIZE}
          onClick={handleCreateNew}
          data-testid="create-incident-btn"
        >
          Create New Incident
        </Button>
      </div>

      <CustomerIncidentsList token={token} onEdit={handleEdit} refreshTrigger={refreshTrigger} />

      {isModalOpen && (
        <CustomerIncidentModal
          incident={editingIncident}
          onClose={handleModalClose}
          onSaveSuccess={handleSaveSuccess}
          token={token}
        />
      )}
    </div>
  );
};
