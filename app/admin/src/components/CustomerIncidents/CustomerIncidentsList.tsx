'use client';

import React, { useState } from 'react';
import { Alert } from '@components/shared/Alert';
import { LoadingIndicator } from '@components/shared/LoadingIndicator';
import Button, {
  Size,
  Theme,
} from '@havenengineering/module-shared-library/dist/components/Button/Button';
import InlineMessaging, {
  InlineMessagingTheme,
} from '@havenengineering/module-shared-library/dist/components/InlineMessaging/InlineMessaging';
import { UserIncident } from './ManageCustomerIncidentsContent';
import { useCustomerIncidents } from '@hooks/useCustomerIncidents';
import { getHeaders } from '@helpers/utils';
import styles from '@styles/components/CustomerIncidents.module.scss';

type CustomerIncidentsListProps = {
  token: string;
  onEdit: (incident: UserIncident) => void;
  refreshTrigger: number;
};

export const CustomerIncidentsList = ({
  token,
  onEdit,
  refreshTrigger,
}: CustomerIncidentsListProps) => {
  const { data, error, isLoading, mutate } = useCustomerIncidents(token);
  const [deleteError, setDeleteError] = useState<string>('');
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const incidents = data?.incidents || [];

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this incident? This action cannot be undone.')) {
      return;
    }

    try {
      setDeletingId(id);
      setDeleteError('');

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_CUSTOMER_VERIFICATION_SERVICE_API_URL}/api/v1/customer-incidents/${id}`,
        {
          method: 'DELETE',
          headers: getHeaders(token),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete incident');
      }

      // Refresh the incidents list
      await mutate();
    } catch (err) {
      setDeleteError((err as Error).message);
    } finally {
      setDeletingId(null);
    }
  };

  // Refresh data when refreshTrigger changes
  React.useEffect(() => {
    if (refreshTrigger > 0) {
      mutate();
    }
  }, [refreshTrigger, mutate]);

  if (isLoading) {
    return (
      <div data-testid="incidents-loading">
        <LoadingIndicator text="Loading customer incidents..." />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        severity="error"
        title="Error Loading Incidents"
        text={error.message}
        body={
          <Button theme={Theme.SECONDARY_THEME} scale={Size.SMALL_SIZE} onClick={() => mutate()}>
            Retry
          </Button>
        }
      />
    );
  }

  if (!incidents.length) {
    return (
      <div className={styles.emptyState}>
        <p>No customer incidents found.</p>
        <p>Click "Create New Incident" to add the first incident report.</p>
      </div>
    );
  }

  return (
    <div className={styles.incidentsList}>
      {deleteError && (
        <InlineMessaging
          type={InlineMessagingTheme.ERROR_THEME}
          message={deleteError}
          dismissible={true}
          onDismiss={() => setDeleteError('')}
          className={styles.mb15}
        />
      )}

      <div className={styles.tableContainer}>
        <table className={styles.incidentsTable}>
          <thead>
            <tr>
              <th>#</th>
              <th>Booking Reference</th>
              <th>Lead Guest Name</th>
              <th>Reason for Ban</th>
              <th>After Departure</th>
              <th>Police Involved</th>
              <th>Created Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {incidents.map((incident, index) => (
              <tr key={incident.id}>
                <td>{index + 1}</td>
                <td>{incident.bookingReferenceNumber}</td>
                <td>{incident.leadGuestName}</td>
                <td className={styles.reasonCell}>
                  <div className={styles.reasonText} title={incident.reasonForBan}>
                    {incident.reasonForBan}
                  </div>
                </td>
                <td>
                  <span className={incident.isDiscoveredAfterDeparture ? styles.yes : styles.no}>
                    {incident.isDiscoveredAfterDeparture ? 'Yes' : 'No'}
                  </span>
                </td>
                <td>
                  <span className={incident.isPoliceInvolved ? styles.yes : styles.no}>
                    {incident.isPoliceInvolved ? 'Yes' : 'No'}
                  </span>
                </td>
                <td>
                  {incident.createdAt ? new Date(incident.createdAt).toLocaleDateString() : '-'}
                </td>
                <td className={styles.actionsCell}>
                  <div className={styles.actions}>
                    <button
                      className={styles.iconButton}
                      onClick={() => onEdit(incident)}
                      data-testid={`edit-incident-${incident.id}`}
                      title="Edit incident"
                      aria-label="Edit incident"
                    >
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className={styles.actionIcon}
                      >
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                        <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                      </svg>
                    </button>
                    <button
                      className={`${styles.iconButton} ${styles.deleteButton}`}
                      onClick={() => {
                        if (incident.id) {
                          void handleDelete(incident.id);
                        }
                      }}
                      disabled={deletingId === incident.id}
                      data-testid={`delete-incident-${incident.id}`}
                      title="Delete incident"
                      aria-label="Delete incident"
                    >
                      {deletingId === incident.id ? (
                        <div className={styles.spinner}></div>
                      ) : (
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className={styles.actionIcon}
                        >
                          <polyline points="3,6 5,6 21,6" />
                          <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2" />
                          <line x1="10" y1="11" x2="10" y2="17" />
                          <line x1="14" y1="11" x2="14" y2="17" />
                        </svg>
                      )}
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
